Excellent. We will now generate the definitive, final version of the Product Requirements Document. This version will be comprehensive, incorporating all refined workflows, edge cases, and the new 3-tier subscription model. It is designed to be the complete blueprint for the AI development agent.

I will present it section by section for clarity and your final approval.

---

**Definitive Product Requirements Document: Retail Inventory & Finance Manager**

**Version:** 1.0 (Final for Technical Planning)
**Date:** (Current Date)

---

**1. Introduction / Core Problem Statement**

- **Purpose for AI:** To understand the foundational context and the primary problem the application is designed to solve, guiding its understanding of the application's domain.
- **Content:**
  Small to mid-sized retail shops in Uzbekistan predominantly rely on manual, paper-based systems for inventory and financial management. This leads to:
  - **Excessive Time Allocation:** Significant operational hours consumed by manual stock checks and financial reconciliation.
  - **Obscured Financial Performance:** Difficulty in accurately determining daily, monthly, or overall profitability, hindering informed decision-making.
  - **Poor Sales Data Visibility:** Lack of precise data regarding product sales volumes, trends, and performance.
  - **High Error Incidence:** Manual tracking is susceptible to errors, omissions (e.g., unrecorded goods borrowed by customers), and data inconsistencies.
  - **Suboptimal Business Strategy:** Inability to effectively optimize stock, pricing, or strategic direction due to a lack of accurate inventory turnover and profit margin data per item.
    This application is intended to provide a digital, automated solution to these core problems.

---

**2. Product Goals / Core Functional Objectives**

- **Purpose for AI:** To define the high-level success conditions and core capabilities the AI must implement.
- **Content:**
  The primary objective is to develop a web application that enables small to mid-sized retail shop owners in Uzbekistan to manage inventory and finances digitally and professionally, replacing manual methods.
  - **Core Functional Objective 1 (Inventory & Product Management):** Implement a system for reliable, near real-time tracking of product stock levels, including additions (purchases), deductions (sales), and adjustments. The system must support a full product lifecycle: creation (including on-the-fly), editing, conditional deletion for mistakes, and deactivation for discontinued items.
  - **Core Functional Objective 2 (Sales Processing):** Implement an efficient, fast, keyboard-centric sales recording mechanism, optimized for manual product lookup. It must support flexible price/quantity entry, price overrides, and a streamlined workflow for both cash and on-account (credit) sales.
  - **Core Functional Objective 3 (Financial Data & Reporting):** Implement functionality to calculate and report on key financial metrics derived from sales and purchase data, specifically sales revenue, cost of goods sold (COGS), and basic profitability. Provide detailed, searchable ledgers for all sales and purchase transactions.
  - **Core Functional Objective 4 (Accounts Management):** Implement a dual-sided accounts management system:
    - **Accounts Receivable:** Track money owed by customers for on-account sales.
    - **Accounts Payable:** Track money the shop owes to suppliers for on-credit purchases.
  - **Core Functional Objective 5 (User & Subscription Management):** Implement a multi-user system with distinct roles (`Shop Owner`, `Shop Staff`). Access to features and data (e.g., number of users, number of products) will be governed by a simulated 3-tier subscription model (Basic, Standard, Premium).

---

This completes the first two sections. They set the high-level stage for the AI.

Please confirm if you are satisfied with this, and we will proceed to **Section 3: Key User Roles & Their Core Functional Interactions/Needs.**
