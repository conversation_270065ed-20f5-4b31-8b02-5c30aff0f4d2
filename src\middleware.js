// src/middleware.js
import NextAuth from "next-auth";
import { authConfig } from "./lib/auth.config.js";

// Initialize NextAuth with the config that includes the `authorized` callback
const { auth } = NextAuth(authConfig);

export default auth;

export const config = {
  // Matcher to specify which routes the middleware should run on.
  matcher: [
    "/dashboard/:path*",
    // Exclude NextAuth's own API routes and public static assets.
    "/((?!api/auth|_next/static|_next/image|favicon.ico|login).*)",
  ],
};
