// src/lib/types.js

/**
 * Represents a user in the system
 * @typedef {object} User
 * @property {string} id - The unique identifier for the user
 * @property {string} name - The user's full name
 * @property {string} email - The user's email address
 * @property {string} [image] - The user's profile image URL
 * @property {string} [role] - The user's role (owner, staff)
 */

/**
 * Represents an authenticated session
 * @typedef {object} Session
 * @property {User} user - The authenticated user
 * @property {string} expires - Session expiration date
 */

/**
 * Represents a feature card on the landing page
 * @typedef {object} FeatureCard
 * @property {string} icon - The icon name from lucide-react
 * @property {string} title - The feature title
 * @property {string} description - The feature description
 */

/**
 * Represents a pricing plan
 * @typedef {object} PricingPlan
 * @property {string} name - The plan name (Basic, Standard, Premium)
 * @property {number} price - The monthly price in UZS
 * @property {string[]} features - List of features included in the plan
 * @property {boolean} [recommended] - Whether this plan is recommended
 */

/**
 * Represents component props with children
 * @typedef {object} ComponentWithChildren
 * @property {React.ReactNode} children - React children elements
 */

/**
 * Represents button variant types
 * @typedef {'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'} ButtonVariant
 */

/**
 * Represents button size types
 * @typedef {'default' | 'sm' | 'lg' | 'icon'} ButtonSize
 */
