// src/lib/auth.config.js
import GoogleProvider from "next-auth/providers/google";

/**
 * @type {import('next-auth').NextAuthConfig}
 */
export const authConfig = {
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
  ],
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
  },
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnDashboard = nextUrl.pathname.startsWith("/dashboard");

      if (isOnDashboard) {
        if (isLoggedIn) return true;
        return false; // Redirect unauthenticated users to login page
      } else if (isLoggedIn) {
        if (nextUrl.pathname === "/login") {
          return Response.redirect(new URL("/dashboard", nextUrl));
        }
      }
      return true;
    },
    async jwt({ token, user, account }) {
      // On initial sign-in, persist user data to the token
      if (account && user) {
        token.id = user.id;
        // Example for future: token.role = 'owner';
      }
      return token;
    },
    async session({ session, token }) {
      // Expose user ID and other custom properties to the client-side session object
      if (token.id && session.user) {
        // @ts-ignore - We are intentionally modifying the session user object
        session.user.id = token.id;
      }
      // if (token.role && session.user) {
      //   // @ts-ignore
      //   session.user.role = token.role;
      // }
      return session;
    },
  },
};
