# Phase 0: Discovery & Authentication - Detailed Implementation Plan

## 1. Technical Architecture & Setup

### 1.1 Required Package Additions

```bash
# Auth.js for Google OAuth
npm install next-auth@5 @auth/core

# UI Components & Styling
npm install @radix-ui/react-dialog @radix-ui/react-icons
npm install framer-motion
npm install @radix-ui/react-avatar
```

### 1.2 Project Structure Updates

```
src/
├── app/
│   ├── (auth)/
│   │   └── login/
│   │       └── page.jsx       # Login page with modal trigger
│   ├── api/
│   │   └── auth/
│   │       └── [...nextauth]/
│   │           └── route.js   # Auth.js API routes
│   └── (marketing)/          # Public routes group
│       └── page.jsx          # Landing page
├── components/
│   ├── auth/
│   │   ├── auth-modal.jsx
│   │   └── google-signin-button.jsx
│   ├── marketing/
│   │   ├── app-header.jsx    # Public header
│   │   ├── app-footer.jsx
│   │   ├── feature-card.jsx
│   │   ├── pricing-card.jsx
│   │   └── hero-section.jsx
│   └── ui/                   # Already exists with shadcn
├── lib/
│   └── auth.js              # Auth.js configuration
└── styles/
    └── marketing.css        # Marketing-specific styles
```

## 2. Implementation Plan & Best Practices Application

### 2.1 Authentication Setup (Priority: High)

- Apply principles from `authjs-guide.md`
- Implement Route Groups for auth and marketing sections
- Server Components for API routes and pages
- Client Components only for interactive elements (modal, buttons)

### 2.2 UI Components Implementation

- Apply `app-architecture-principles-soc-reusability-guide.md`
- Implement atomic design principles
- Create reusable components with prop validation
- Use CSS variables for design tokens

#### Component Hierarchy:

1. Base Components (Already available via shadcn)

   - Button
   - Dialog (Modal)
   - Card
   - Typography components

2. Marketing Components (New)

   - AppHeader (Server Component)
   - AppFooter (Server Component)
   - FeatureCard (Server Component)
   - PricingCard (Server Component)
   - HeroSection (Server Component)

3. Auth Components (New)
   - AuthModal (Client Component)
   - GoogleSignInButton (Client Component)

### 2.3 Design System & Styling

- Extend existing shadcn theme
- Define color palette in tailwind.config.js
- Create marketing-specific utility classes
- Implement responsive design patterns

## 3. Performance Optimization

Based on `app-performance-guide.md`:

- Implement static page generation for marketing pages
- Optimize images with next/image
- Implement proper font loading strategy
- Use suspense boundaries for dynamic imports

## 4. Security Implementation

Based on `app-security-guide.md`:

- Implement CSP headers
- Set up proper CORS configuration
- Configure Auth.js security options
- Implement rate limiting for auth endpoints

## 6. Reusability Analysis

### Existing Assets to Leverage:

1. UI Components:

   - Button component from shadcn
   - Other shadcn components as needed

2. Utility Functions:
   - cn utility from lib/utils.js

### New Reusable Assets to Create:

1. Components:

   - Marketing components (designed for reuse in future marketing pages)
   - Auth components (reusable across different auth flows)

2. Utilities:
   - Auth helpers
   - Marketing-specific utilities

## 7. Development Phases

### Phase 0.1: Foundation

1. Set up Auth.js
2. Configure route groups
3. Implement basic security measures

### Phase 0.2: Marketing Components

1. Implement base marketing components
2. Build landing page layout
3. Add responsive styles

### Phase 0.3: Authentication

1. Implement AuthModal
2. Set up Google OAuth
3. Create auth flow handlers

### Phase 0.4: Polish & Optimization

1. Add animations and transitions
2. Implement performance optimizations
3. Add error handling
4. Test and debug

## 8. Quality Guidelines

### Code Quality

Apply principles from `clean-code-guide.md`:

- Consistent naming conventions
- Proper component organization
- Clear separation of concerns
- Proper error handling
- Comprehensive documentation

### Performance Metrics

- Lighthouse score targets:
  - Performance: 90+
  - Accessibility: 95+
  - Best Practices: 95+
  - SEO: 95+

### Accessibility Requirements

- WCAG 2.1 AA compliance
- Proper ARIA attributes
- Keyboard navigation
- Screen reader compatibility

## 9. CLI Commands for Setup

```bash
# Generate route group directories
mkdir src/app/(auth) src/app/(marketing)

# Generate component directories
mkdir -p src/components/{auth,marketing}

# Generate auth API routes
mkdir -p src/app/api/auth/[...nextauth]
```

## 10. Environment Variables Required

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```
